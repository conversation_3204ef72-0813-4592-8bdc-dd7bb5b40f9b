using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using attendance.Services;
using attendance.DataAccess;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Hosting;

namespace attendance
{

    public class Program
    {
        public static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(args);

            // Configuration
            builder.Services.Configure<AppSettings>(builder.Configuration.GetSection("AppSettings"));


            // Logging
            builder.Logging.ClearProviders();
            builder.Logging.AddConsole();
            builder.Logging.AddFile("Logs/app-{Date}.txt");

            // Database
            builder.Services.AddFreeSql(provider =>
                new FreeSql.FreeSqlBuilder()
                    .UseConnectionString(FreeSql.DataType.Sqlite,
                        builder.Configuration.GetConnectionString("Default"))
                    .UseAdoConnectionPool(true)
                    //.UseMonitorCommand(cmd =>
                    //    provider.GetRequiredService<ILogger<Program>>()
                    //        .LogInformation($"Sql: {cmd.CommandText}"))
                    .UseAutoSyncStructure(true)
                    .Build());

            // ��������������
            builder.Services.AddCors(
                options =>
                {
                    options.AddPolicy("AllowAll", policy => policy.AllowAnyOrigin().AllowAnyHeader().AllowAnyMethod());
                });

            // Services
            builder.Services
                .AddLogging()
                .AddSingleton<FaceDeviceService>()
                .AddSingleton<PersonRepository>()
                .AddSingleton<PersonRecordRepository>()
                .AddHostedService<PersonSyncService>()
                .AddHostedService<PersonRecordSyncService>()
                .AddControllersWithViews();

            var app = builder.Build();

            // Environment configuration
            if (app.Environment.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
            else
            {
                app.UseExceptionHandler("/error");
                app.UseHsts();
            }

            app.UseRouting();
            app.UseCors("AllowAll");
            app.MapControllers();

            await app.RunAsync();
        }
    }

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddFreeSql(this IServiceCollection services,
            Func<IServiceProvider, IFreeSql> factory)
        {
            services.AddSingleton(factory);
            return services;
        }
    }
}