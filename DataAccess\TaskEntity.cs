using System;

namespace attendance.DataAccess
{
    /// <summary>
    /// 设备任务实体
    /// </summary>
    public class TaskEntity
    {
        /// <summary>
        /// 任务ID
        /// </summary>
        public long Id { get; set; }

        /// <summary>
        /// 任务编号（自定义名称）
        /// </summary>
        public string TaskNo { get; set; }

        /// <summary>
        /// 设备唯一标识
        /// </summary>
        public string DeviceKey { get; set; }

        /// <summary>
        /// 接口名称（如：setPassWord, openDoorControl等）
        /// </summary>
        public string InterfaceName { get; set; }

        /// <summary>
        /// 任务参数（JSON格式，根据不同接口存储不同的参数）
        /// </summary>
        public string Parameters { get; set; }

        /// <summary>
        /// 是否需要处理结果
        /// </summary>
        public bool NeedResult { get; set; }

        /// <summary>
        /// 任务状态（0：待执行，1：执行中，2：执行成功，3：执行失败）
        /// </summary>
        public int Status { get; set; }

        /// <summary>
        /// 执行结果（JSON格式，存储执行后的返回结果）
        /// </summary>
        public string Result { get; set; }

        /// <summary>
        /// 错误信息
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 重试次数
        /// </summary>
        public int RetryCount { get; set; }

        /// <summary>
        /// 最大重试次数
        /// </summary>
        public int MaxRetryCount { get; set; }

        /// <summary>
        /// 任务优先级（数字越小优先级越高）
        /// </summary>
        public int Priority { get; set; }

        /// <summary>
        /// 计划执行时间
        /// </summary>
        public DateTime? ScheduledTime { get; set; }

        /// <summary>
        /// 开始执行时间
        /// </summary>
        public DateTime? ExecuteStartTime { get; set; }

        /// <summary>
        /// 执行完成时间
        /// </summary>
        public DateTime? ExecuteEndTime { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        public DateTime CreateTime { get; set; }

        /// <summary>
        /// 更新时间
        /// </summary>
        public DateTime UpdateTime { get; set; }

        /// <summary>
        /// 任务超时时间（单位：秒）
        /// </summary>
        public int Timeout { get; set; }

        /// <summary>
        /// 备注
        /// </summary>
        public string Remarks { get; set; }
    }
}
