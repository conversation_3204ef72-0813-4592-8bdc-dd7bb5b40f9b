﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace attendance.DataAccess
{
    public class PersonRecordRepository : SQLiteDbHelper<RecordEntity>
    {
        public PersonRecordRepository(IFreeSql freeSql) : base(freeSql)
        {
        }

        public string GetLatestRecordTime(string name)
        {
            var entity = _fsql.Select<RecordEntity>().Where(p => p.Name == name).OrderByDescending(p => p.Time).First();
            if (entity == null)
            {
                return "";
            }
            else
            {
                // 加上1000毫秒,防止每次还会重复获取到最新的一条记录
                return DateTimeOffset.FromUnixTimeMilliseconds(entity.Time + 1000).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        public List<RecordEntity> GetRecordsByNameAndTime(string name, string startTime, string endTime)
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentNullException();
            }
            if (string.IsNullOrWhiteSpace(startTime) && string.IsNullOrWhiteSpace(endTime))
            {
                return _fsql.Select<RecordEntity>().Where(p => p.Name == name).OrderByDescending(p => p.Time).ToList();
            }
            else if ((string.IsNullOrWhiteSpace(startTime) && !string.IsNullOrWhiteSpace(endTime)))
            {
                var end = DateTimeOffset.Parse(endTime).ToUnixTimeMilliseconds();
                return _fsql.Select<RecordEntity>().Where(p => p.Name == name && p.Time <= end).OrderByDescending(p => p.Time).ToList();
            }
            else if (!string.IsNullOrWhiteSpace(startTime) && string.IsNullOrWhiteSpace(endTime))
            {
                var start = DateTimeOffset.Parse(startTime).ToUnixTimeMilliseconds();
                return _fsql.Select<RecordEntity>().Where(p => p.Name == name && p.Time >= start).OrderByDescending(p => p.Time).ToList();
            }
            else
            {
                var start = DateTimeOffset.Parse(startTime).ToUnixTimeMilliseconds();
                var end = DateTimeOffset.Parse(endTime).ToUnixTimeMilliseconds();
                return _fsql.Select<RecordEntity>().Where(p => p.Name == name && p.Time >= start && p.Time <= end).OrderByDescending(p => p.Time).ToList();
            }

        }
    }
}
