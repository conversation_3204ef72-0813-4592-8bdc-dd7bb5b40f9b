using System;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

public class RestService
{
    private static readonly HttpClient _httpClient;

    static RestService()
    {
        _httpClient = new HttpClient();
    }

    /// <summary>
    /// 通用方法，用于访问 REST 接口并获取 JSON 字符串，然后将其转换为指定的泛型对象。
    /// </summary>
    /// <typeparam name="T">要转换的目标类型。</typeparam>
    /// <param name="url">REST 接口的 URL。</param>
    /// <returns>返回指定类型的对象。</returns>
    public static async Task<T> GetAsync<T>(string url)
    {
        try
        {
            // 发送 GET 请求并获取响应
            HttpResponseMessage response = await _httpClient.GetAsync(url);
            response.EnsureSuccessStatusCode(); // 确保请求成功

            // 读取响应内容
            string jsonResponse = await response.Content.ReadAsStringAsync();

            // 将 JSON 字符串反序列化为目标类型对象
            T result = JsonConvert.DeserializeObject<T>(jsonResponse);

            return result;
        }
        catch (HttpRequestException ex)
        {
            // 处理 HTTP 请求异常
            Console.WriteLine($"HTTP 请求错误: {ex.Message}");
            throw;
        }
        catch (JsonException ex)
        {
            // 处理 JSON 反序列化异常
            Console.WriteLine($"JSON 反序列化错误: {ex.Message}");
            throw;
        }
    }



    public static async Task<T> PostAsyncByForm<T>(string url, Dictionary<string, string> formData)
    {
        // 将对象转换为表单数据
        var content = new FormUrlEncodedContent(formData);

        // 发送POST请求
        var response = await _httpClient.PostAsync(url, content);

        // 确保响应状态码为200
        response.EnsureSuccessStatusCode();

        // 读取响应内容
        var responseContent = await response.Content.ReadAsStringAsync();

        // 将JSON字符串转换为泛型对象
        T result = JsonConvert.DeserializeObject<T>(responseContent);

        return result;
    }


    public static async Task<T> PostAsyncByForm<T>(string url, string jsonData)
    {
        // 将对象转换为表单数据
        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");

        // 发送POST请求
        var response = await _httpClient.PostAsync(url, content);

        // 确保响应状态码为200
        response.EnsureSuccessStatusCode();

        // 读取响应内容
        var responseContent = await response.Content.ReadAsStringAsync();

        // 将JSON字符串转换为泛型对象
        T result = JsonConvert.DeserializeObject<T>(responseContent);

        return result;
    }


    public static async Task<T> PostAsyncByJson<T>(string url, string jsonData)
    {

        var content = new StringContent(jsonData, Encoding.UTF8, "application/json");

        // 发送POST请求
        var response = await _httpClient.PostAsync(url, content);

        // 确保响应状态码为200
        response.EnsureSuccessStatusCode();

        // 读取响应内容
        var responseContent = await response.Content.ReadAsStringAsync();

        // 将JSON字符串转换为泛型对象
        T result = JsonConvert.DeserializeObject<T>(responseContent);

        return result;
    }
}