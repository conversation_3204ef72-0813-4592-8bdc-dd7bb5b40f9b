using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace attendance
{

    /// <summary>
    /// 识别记录
    /// </summary>
    public class NewRecordFindResult : BaseResponseResult<RecordData>
    {

    }

    public class PageInfo
    {
        public int index;
        public int length;
        public int size;
        public int total;
    }

    public class Record
    {
        public int aliveType;
        public string id;
        public string idcardNum;
        public int identifyType;
        public int isImgDeleted;
        public bool isPass;
        public int model;
        public string name;
        public int passTimeType;
        public string path;
        public int permissionTimeType;
        public string personId;
        public int recModeType;
        public int recType;
        public int state;
        public long time;
        public int type;
    }

    public class RecordData
    {
        public PageInfo pageInfo;
        public List<Record> records;
    }

}
