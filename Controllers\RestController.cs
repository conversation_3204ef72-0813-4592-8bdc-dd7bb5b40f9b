using Microsoft.AspNetCore.Mvc;
using attendance.DataAccess;
using System.Collections.Generic;
using System.Linq;
using Microsoft.AspNetCore.Cors;

namespace attendance.Controllers
{
    [ApiController]
    [Route("api/")]
    [EnableCors]
    public class RestController : ControllerBase
    {
        private readonly PersonRepository _personRepository;
        private readonly PersonRecordRepository _recordRepository;

        public RestController(PersonRepository personRepository, PersonRecordRepository recordRepository)
        {
            _personRepository = personRepository;
            _recordRepository = recordRepository;
        }

        [HttpGet("persons")] // 获取所有人员
        public ActionResult<List<Person>> GetAllPersons()
        {
            var persons = _personRepository.GetAll();
            return Ok(persons);
        }

        [HttpGet("records")] // 根据姓名、开始时间和结束时间获取记录
        public ActionResult<List<RecordEntity>> GetRecords(string name, [FromQuery] string? startTime, [FromQuery] string? endTime)
        {
            var records = _recordRepository.GetRecordsByNameAndTime(name, startTime, endTime);
            return Ok(records.ConvertAll<object>(r => new
            {
                r.Id,
                r.IdcardNum,
                r.Name,
                r.PersonId,
                Time = DateTimeOffset.FromUnixTimeMilliseconds(r.Time).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss")
            }));
        }

        [HttpGet("records/{personName}")] // 根据人员姓名、开始时间和结束时间获取记录
        public ActionResult<List<RecordEntity>> GetRecordsByPerson([FromRoute] string personName, string? startTime, string? endTime)
        {
            var records = _recordRepository.GetRecordsByNameAndTime(personName, startTime, endTime);
            return Ok(records.ConvertAll<object>(r => new
            {
                r.Id,
                r.IdcardNum,
                r.Name,
                r.PersonId,
                Time = DateTimeOffset.FromUnixTimeMilliseconds(r.Time).LocalDateTime.ToString("yyyy-MM-dd HH:mm:ss")
            }));
        }
    }
}
