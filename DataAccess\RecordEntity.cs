using FreeSql.DataAnnotations;
using System;

namespace attendance.DataAccess
{
    [Table(Name = "record")]
    public class RecordEntity
    {
        [Column(IsPrimary = true)]
        public string? Id { get; set; }

        [Column(Name = "PersonId")]
        public string? PersonId { get; set; }

        [Column(Name = "Name")]
        public string? Name { get; set; }

        [Column(Name = "IdcardNum")]
        public string? IdcardNum { get; set; }

        [Column(Name = "Time")]
        public long Time { get; set; }

        [Column(Name = "AliveType")]
        public int AliveType { get; set; }

        [Column(Name = "IdentifyType")]
        public int IdentifyType { get; set; }

        [Column(Name = "IsImgDeleted")]
        public int IsImgDeleted { get; set; }

        [Column(Name = "IsPass")]
        public bool IsPass { get; set; }

        [Column(Name = "Model")]
        public int Model { get; set; }

        [Column(Name = "PassTimeType")]
        public int PassTimeType { get; set; }

        [Column(Name = "Path")]
        public string? Path { get; set; }

        [Column(Name = "PermissionTimeType")]
        public int PermissionTimeType { get; set; }

        [Column(Name = "RecModeType")]
        public int RecModeType { get; set; }

        [Column(Name = "RecType")]
        public int RecType { get; set; }

        [Column(Name = "State")]
        public int State { get; set; }

        [Column(Name = "Type")]
        public int Type { get; set; }
    }
}
