using FreeSql;
using System.Linq.Expressions;

namespace attendance.DataAccess
{
    public class PersonRepository : SQLiteDbHelper<PersonEntity>
    {
        public PersonRepository(IFreeSql freeSql) : base(freeSql)
        {
        }

        /// <summary>
        /// 新增或更新人员信息
        /// </summary>
        /// <param name="person">人员实体</param>
        /// <returns>受影响的行数</returns>
        public int InsertOrUpdate(PersonEntity person)
        {
            if (person == null)
                throw new ArgumentNullException(nameof(person));

            // 根据Name(主键)查找是否存在
            var existingPerson = GetByName(person.Name);

            if (existingPerson == null)
            {
                // 不存在则插入
                return Insert(person);
            }
            else
            {
                // 存在则更新
                return Update(person);
            }
        }

        /// <summary>
        /// 根据姓名获取人员信息
        /// </summary>
        /// <param name="name">姓名</param>
        /// <returns>人员实体</returns>
        public PersonEntity GetByName(string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            return _fsql.Select<PersonEntity>()
                .Where(p => p.Name == name)
                .First();
        }

        /// <summary>
        /// 根据条件查询人员列表
        /// </summary>
        /// <param name="predicate">查询条件</param>
        /// <returns>人员列表</returns>
        public List<PersonEntity> GetList(Expression<Func<PersonEntity, bool>> predicate = null)
        {
            var query = _fsql.Select<PersonEntity>();

            if (predicate != null)
            {
                query = query.Where(predicate);
            }

            return query.ToList();
        }

        /// <summary>
        /// 根据姓名删除人员
        /// </summary>
        /// <param name="name">姓名</param>
        /// <returns>受影响的行数</returns>
        public int DeleteByName(string name)
        {
            if (string.IsNullOrEmpty(name))
                throw new ArgumentNullException(nameof(name));

            return _fsql.Delete<PersonEntity>()
                .Where(p => p.Name == name)
                .ExecuteAffrows();
        }

        /// <summary>
        /// 批量新增或更新人员信息
        /// </summary>
        /// <param name="persons">人员实体列表</param>
        /// <returns>受影响的行数</returns>
        public int BatchInsertOrUpdate(IEnumerable<PersonEntity> persons)
        {
            if (persons == null)
                throw new ArgumentNullException(nameof(persons));

            int affectedRows = 0;
            foreach (var person in persons)
            {
                affectedRows += InsertOrUpdate(person);
            }
            return affectedRows;
        }


    }
}
