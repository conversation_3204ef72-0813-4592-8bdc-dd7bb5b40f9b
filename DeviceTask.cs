using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace attendance
{
    public class DeviceTask
    {
        private string taskNo;  //任务名,自定义
        private string interfaceName; //接口名称(需传系统支持的接口名称)
        private bool result; //是否需要处理,默认为true
        // 其余参数附加在固定参数之后
        private Dictionary<string, string> paramsDic;
    }
}
