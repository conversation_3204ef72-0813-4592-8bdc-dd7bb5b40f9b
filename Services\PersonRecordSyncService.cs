using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;
using attendance.DataAccess;
using System.Collections.Generic;
using System.Linq;

namespace attendance.Services
{
    public class PersonRecordSyncService : BackgroundService
    {
        private readonly ILogger<PersonRecordSyncService> _logger;
        private readonly FaceDeviceService _deviceService;
        private readonly PersonRepository _personRepository;
        private readonly PersonRecordRepository _recordRepository;

        // 可配置的同步间隔（分钟）
        private int _syncIntervalMinutes = 1;

        public PersonRecordSyncService(
            ILogger<PersonRecordSyncService> logger,
            FaceDeviceService deviceService,
            PersonRepository personRepository,
            PersonRecordRepository recordRepository)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            _personRepository = personRepository ?? throw new ArgumentNullException(nameof(personRepository));
            _recordRepository = recordRepository ?? throw new ArgumentNullException(nameof(personRepository));
        }

        public void SetSyncInterval(int minutes)
        {
            if (minutes < 1)
            {
                throw new ArgumentException("Interval must be at least 1 minute", nameof(minutes));
            }
            _syncIntervalMinutes = minutes;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await SyncPersonRecordsAsync(stoppingToken);
                    await Task.Delay(TimeSpan.FromMinutes(_syncIntervalMinutes), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Person record synchronization service is stopping");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in person record synchronization service");
                    // 出错后等待1分钟再重试
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
            }
        }

        private async Task SyncPersonRecordsAsync(CancellationToken cancellationToken)
        {
            try
            {
                _logger.LogInformation("开始同步人员进出记录...");

                // 1. 获取所有人员列表
                var persons = _personRepository.GetAll();
                _logger.LogInformation($"获取到 {persons.Count} 个人员");

                foreach (var person in persons)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        // 2. 获取每个人员的识别记录
                        // 做个小优化,只获取最新的当天的记录
                        string startDate = _recordRepository.GetLatestRecordTime(person.Name);
                        var recordResult = _deviceService.GetRecordResultByPersonAndStartTime(person.Id, startDate);

                        _logger.LogInformation($"开始获取人员 {person.Id} ({person.Name}) 的记录,记录开始时间: {startDate}");
                        if (recordResult?.data?.records == null || recordResult?.data?.records.Count == 0)
                        {
                            _logger.LogInformation($"人员 {person.Id} ({person.Name}) 没有识别记录");
                            continue;
                        }

                        _logger.LogInformation($"获取到人员 {person.Id} ({person.Name}) 的 {recordResult.data.records.Count} 条记录");

                        // 处理第一页数据
                        await ProcessRecordsAsync(recordResult.data.records, person.Name, cancellationToken);

                        // 如果有更多页，继续获取
                        int currentPage = recordResult.data.pageInfo.index;
                        int totalRecords = recordResult.data.pageInfo.total;
                        int pageSize = recordResult.data.pageInfo.length;
                        int pageCount = recordResult.data.pageInfo.size;
                        while (currentPage + 1 < pageCount) // 判断是否还有更多记录
                        {
                            if (cancellationToken.IsCancellationRequested)
                                break;

                            currentPage++;
                            recordResult = _deviceService.GetRecordResult(person.Id, startDate, "", 1000, null, null, currentPage);

                            if (recordResult?.data?.records == null || recordResult.data.records.Count == 0)
                                break;

                            _logger.LogInformation($"获取到人员 {person.Id} ({person.Name}) 的第 {currentPage + 1} 页记录，共 {recordResult.data.records.Count} 条");
                            await ProcessRecordsAsync(recordResult.data.records, person.Name, cancellationToken);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"处理人员 {person.Id} 的记录时发生错误");
                    }
                }

                _logger.LogInformation("人员进出记录同步完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "同步人员进出记录时发生错误");

            }
        }

        private async Task ProcessRecordsAsync(List<Record> records, string personName, CancellationToken cancellationToken)
        {
            if (records == null || !records.Any())
                return;

            try
            {
                // 只获取特定人员的所有记录ID
                var existingIds = _recordRepository.GetAll()
                    .Where(r => r.Name == personName)
                    .Select(r => r.Id)
                    .ToHashSet();

                foreach (var record in records)
                {
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    try
                    {
                        // 使用内存中的HashSet检查重复
                        if (existingIds.Contains(record.id))
                        {
                            continue; // 记录已存在，跳过
                        }

                        // 创建新的记录实体
                        var recordEntity = record.ToRecordEntity();

                        // 保存到数据库
                        await Task.Run(() => _recordRepository.Insert(recordEntity), cancellationToken);
                        _logger.LogDebug($"保存记录: {record.id}, 人员: {record.name}, 时间: {record.time}");

                        // 将新保存的记录ID添加到HashSet中
                        existingIds.Add(record.id);
                    }
                    catch (Exception ex) when (ex is not OperationCanceledException)
                    {
                        _logger.LogError(ex, $"保存记录 {record.id} 时发生错误");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量处理记录时发生错误");
            }
        }
    }
}
