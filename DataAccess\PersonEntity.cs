using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using FreeSql.DataAnnotations;

namespace attendance.DataAccess
{
    [Table(Name = "person")]
    public class PersonEntity
    {

        [Column(Name = "CreateTime")]
        public long CreateTime{ get; set; }
        public int FaceAndCardPermission { get; set; }
        public int FaceAndPasswordPermission { get; set; }
        public int FacePermission { get; set; }
        public string? IDNumber { get; set; }

        [Column(IsNullable =false)]
        public string? Id { get; set; }
        public int IdCardPermission { get; set; }
        public string? IdcardNum { get; set; }

        [Column(IsPrimary = true)]
        public string? Name { get; set; }
        public string? Password { get; set; }
        public int PasswordPermission { get; set; }
        public string? Phone { get; set; }
        public int Role { get; set; }
        public string? Tag { get; set; }
    }
}
