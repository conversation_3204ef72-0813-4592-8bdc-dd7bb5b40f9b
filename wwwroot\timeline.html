<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>考勤记录时间轴</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .timeline-container {
            width: 100%;
            overflow-x: auto;
            padding: 20px 0;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .timeline {
            display: flex;
            position: relative;
            min-width: max-content;
            padding: 20px 40px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            top: 50%;
            height: 2px;
            background: #e0e0e0;
            z-index: 1;
        }

        .timeline-item {
            position: relative;
            padding: 0 30px;
            z-index: 2;
        }

        .timeline-point {
            width: 16px;
            height: 16px;
            background: #1890ff;
            border-radius: 50%;
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .timeline-point:hover {
            background: #40a9ff;
            transform: translate(-50%, -50%) scale(1.2);
        }

        .timeline-content {
            background: white;
            padding: 12px;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            width: 200px;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: -120px;
        }

        .timeline-content.bottom {
            top: auto;
            bottom: -120px;
        }

        .timeline-content h3 {
            margin: 0 0 8px;
            font-size: 14px;
            color: #333;
        }

        .timeline-content p {
            margin: 4px 0;
            font-size: 12px;
            color: #666;
        }

        .timeline-time {
            position: absolute;
            bottom: -30px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 12px;
            color: #999;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="timeline-container">
        <div class="timeline" id="timeline"></div>
    </div>

    <script>
        // 将Unix时间戳转换为时分秒格式
        function formatTime(timestamp) {
            const date = new Date(timestamp * 1000);
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });
        }
        // 定义API端点
        const API_ENDPOINT = 'http://*************:5002/api/records/郭刚';

        // 从API获取考勤记录
        async function fetchAttendanceRecords() {
            try {
                const response = await fetch(API_ENDPOINT);
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                const records = await response.json();
                initTimeline(records);
            } catch (error) {
                console.error('获取考勤记录失败:', error);
                // 显示错误信息给用户
                const timeline = document.getElementById('timeline');
                timeline.innerHTML = '<div style="color: red;">加载考勤记录失败，请稍后重试</div>';
            }
        }

        // 页面加载完成后获取数据
        document.addEventListener('DOMContentLoaded', fetchAttendanceRecords);
        // 示例数据
        const records = [
            {
                "id": "77541",
                "idcardNum": "167816941",
                "name": "郭刚",
                "personId": "BEW0114",
                "time": "2025-02-24 18:06:19"
            }
        ];

        function createTimelineItem(record, index) {
            // 创建左侧名字显示区域
            const nameLabel = document.createElement('div');
            nameLabel.style.position = 'absolute';
            nameLabel.style.left = '100px';  // 将名字显示在时间轴左侧
            nameLabel.style.top = '50%';
            nameLabel.style.transform = 'translateY(-50%)';
            nameLabel.style.fontSize = '14px';
            nameLabel.style.color = '#333';
            nameLabel.textContent = record.name;
            const item = document.createElement('div');
            item.className = 'timeline-item';

            const point = document.createElement('div');
            point.className = 'timeline-point';

            const content = document.createElement('div');
            content.className = `timeline-content ${index % 2 === 0 ? 'bottom' : ''}`;

            const time = document.createElement('div');
            time.className = 'timeline-time';
            time.textContent = new Date(record.time).toLocaleTimeString();

            content.innerHTML = `
                <h3>${record.name}</h3>
                <p>工号: ${record.personId}</p>
                <p>卡号: ${record.idcardNum}</p>
                <p>时间: ${formatTime(record.time)}</p>
            `;

            item.appendChild(point);
            //item.appendChild(content);
            item.appendChild(time);

            return item;
        }

        function initTimeline(records) {
            const timeline = document.getElementById('timeline');
            records.sort((a, b) => new Date(a.time) - new Date(b.time));
            
            records.forEach((record, index) => {
                const item = createTimelineItem(record, index);
                timeline.appendChild(item);
            });
        }

        // 初始化时间轴
        initTimeline(records);

        // 添加横向滚动支持
        const container = document.querySelector('.timeline-container');
        let isDown = false;
        let startX;
        let scrollLeft;

        container.addEventListener('mousedown', (e) => {
            isDown = true;
            container.style.cursor = 'grabbing';
            startX = e.pageX - container.offsetLeft;
            scrollLeft = container.scrollLeft;
        });

        container.addEventListener('mouseleave', () => {
            isDown = false;
            container.style.cursor = 'default';
        });

        container.addEventListener('mouseup', () => {
            isDown = false;
            container.style.cursor = 'default';
        });

        container.addEventListener('mousemove', (e) => {
            if (!isDown) return;
            e.preventDefault();
            const x = e.pageX - container.offsetLeft;
            const walk = (x - startX) * 2;
            container.scrollLeft = scrollLeft - walk;
        });
    </script>
</body>
</html>