{"profiles": {"attendance": {"commandName": "Project", "launchBrowser": true, "launchUrl": "http://localhost:5001/api/records?name=郭刚", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:10116;http://localhost:10117"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}", "environmentVariables": {"ASPNETCORE_URLS": "https://+:443;http://+:80"}, "publishAllPorts": true, "useSSL": true}}}