using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace attendance
{

    /// <summary>
    /// 设备配置项
    /// </summary>
    public class FaceDeviceConfig
    {
        public string applicationName;
        public string comModContent;
        public int comModType;
        public string companyName;
        public int delayTimeForCloseDoor;
        public int delayTimeForCloseLed;
        public string displayModContent;
        public string displayModInvalidQrcodeContent;
        public int displayModInvalidQrcodeType;
        public string displayModStrangerContent;
        public int displayModStrangerType;
        public int displayModType;
        public int identifyDistance;
        public int identifyRecordUploadObject;
        public int identifyScores;
        public int isOpenRelay;
        public int isRecognitionOpen;
        public int isSimilarEnable;
        public int multiplayerDetection;
        public int recCardFaceValue;
        public string recDisplayImageMode;
        public int recDoubleValue;
        public int recFaceHookTimeout;
        public string recFailSaveSpotImage;
        public string recFailWiegandContent;
        public int recFailWiegandType;
        public int recModeCardEnable;
        public int recModeCardFaceEnable;
        public int recModeCardFaceHardware;
        public int recModeCardFaceIntf;
        public int recModeCardHardware;
        public int recModeCardIntf;
        public int recModeCardPhoto;
        public int recModeDOBEnable;
        public int recModeFaceEnable;
        public int recModeFacePasswordEnable;
        public int recModeIdcardFaceEnable;
        public int recModeIdcardFaceHardware;
        public int recModeIdcardFaceIntf;
        public int recModePasswordEnable;
        public int recModeQREnable;
        public string recNoPerComModeContent;
        public int recNoPerComModeType;
        public string recNoPerDisplayText1Content;
        public int recNoPerDisplayText1Type;
        public string recNoPerDisplayText2Content;
        public int recNoPerDisplayText2Type;
        public int recNoPerRelayType;
        public string recNoPerSaveSpotImage;
        public string recNoPerTtsModeContent;
        public int recNoPerTtsModeType;
        public string recNoPerWiegandContent;
        public int recNoPerWiegandType;
        public int recRank;
        public int recStrangerTimesThreshold;
        public int recStrangerType;
        public string recSucDisplayText2Content;
        public int recSucDisplayText2Type;
        public string recSucSaveSpotImage;
        public string recSucWiegandContent;
        public int recSucWiegandType;
        public int recType;
        public int regInterval;
        public int relaySwitch;
        public int repeatRegEnable;
        public int saveIdentifyTime;
        public string scrDisplayText1Content;
        public int scrDisplayText1Type;
        public string scrDisplayText2Content;
        public int scrDisplayText2Type;
        public string serialOutContent;
        public int serialOutMode;
        public int showDeviceKey;
        public int showIp;
        public int showPeopleNum;
        public string ttsModContent;
        public string ttsModInvaliedQrcodeContent;
        public int ttsModInvaliedQrcodeType;
        public string ttsModStrangerContent;
        public int ttsModStrangerType;
        public int ttsModType;
        public string wg;
        public int wgInputContentType;
        public int wgInputType;
        public int whitelist;
    }
}
