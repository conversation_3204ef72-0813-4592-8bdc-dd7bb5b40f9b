using FreeSql;
using System.Linq.Expressions;
namespace attendance.DataAccess
{
    public class SQLiteDbHelper<T> where T : class
    {
        protected readonly IFreeSql _fsql;

        public SQLiteDbHelper(IFreeSql freeSql)
        {
            //// 初始化 FreeSql
            //_fsql = new FreeSqlBuilder()
            //    .UseConnectionString(DataType.Sqlite, connectionString)
            //    .UseAutoSyncStructure(true) // 自动同步实体结构到数据库
            //    .Build();
            _fsql = freeSql;
        }

        /// <summary>
        /// 插入数据
        /// </summary>
        /// <param name="entity">要插入的对象</param>
        /// <returns>受影响的行数</returns>
        public int Insert(T entity)
        {
            return _fsql.Insert<T>().AppendData(entity).ExecuteAffrows();
        }

        /// <summary>
        /// 批量插入数据
        /// </summary>
        /// <param name="entities">要插入的对象集合</param>
        /// <returns>受影响的行数</returns>
        public int InsertRange(IEnumerable<T> entities)
        {
            return _fsql.Insert<T>().AppendData(entities).ExecuteAffrows();
        }

        /// <summary>
        /// 更新数据
        /// </summary>
        /// <param name="entity">要更新的对象</param>
        /// <returns>受影响的行数</returns>
        public int Update(T entity)
        {
            return _fsql.Update<T>().SetSource(entity).ExecuteAffrows();
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="predicate">删除条件表达式</param>
        /// <returns>受影响的行数</returns>
        public int Delete(Expression<Func<T, bool>> predicate)
        {
            return _fsql.Delete<T>().Where(predicate).ExecuteAffrows();
        }

        /// <summary>
        /// 查询单个数据
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <returns>查询到的对象，未找到则返回null</returns>
        public T Get(Expression<Func<T, bool>> predicate)
        {
            return _fsql.Select<T>().Where(predicate).First();
        }


        /// <summary>
        /// 查询数据列表
        /// </summary>
        /// <param name="predicate">查询条件表达式</param>
        /// <returns></returns>
        public List<T> GetList(Expression<Func<T, bool>> predicate) => _fsql.Select<T>().Where(predicate).ToList();

        /// <summary>
        /// 查询所有数据
        /// </summary>
        /// <returns>所有数据列表</returns>
        public List<T> GetAll()
        {
            return _fsql.Select<T>().ToList();
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        /// <param name="pageIndex">当前页码</param>
        /// <param name="pageSize">每页大小</param>
        /// <param name="total">输出总记录数</param>
        /// <returns>分页后的数据列表</returns>
        public List<T> GetPageList(int pageIndex, int pageSize, out int total)
        {
            total = (int)_fsql.Select<T>().Count();
            var skip = (pageIndex - 1) * pageSize;
            return _fsql.Select<T>().Skip(skip).Take(pageSize).ToList();
        }
    }
}
