# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Key Commands
- Build: `dotnet build`
- Run: `dotnet run`
- Test: `dotnet test`

## Project Structure
- Main entry point: `Program.cs`
- REST APIs are implemented in `Controllers/RestController.cs`
- Data access is handled by repositories in the `DataAccess` directory
- Configuration is managed via `appsettings.json`

The project is a .NET application with SQLite database integration, primarily focused on attendance tracking functionality.