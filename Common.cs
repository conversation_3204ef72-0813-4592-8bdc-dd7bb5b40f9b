using attendance.DataAccess;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace attendance
{
    public static class Common
    {
        internal static Person ToPerson(this PersonEntity personEntity)
        {
            if (personEntity == null)
                throw new ArgumentNullException(nameof(personEntity));

            return new Person()
            {
                createTime = personEntity.CreateTime,
                faceAndCardPermission = personEntity.FaceAndCardPermission,
                faceAndPasswordPermission = personEntity.FaceAndPasswordPermission,
                facePermission = personEntity.FacePermission,
                id = personEntity.Id ?? string.Empty,
                idcardNum = personEntity.IdcardNum ?? string.Empty,
                idCardPermission = personEntity.IdCardPermission,
                iDNumber = personEntity.IDNumber ?? string.Empty,
                name = personEntity.Name ?? string.Empty,
                password = personEntity.Password ?? string.Empty,
                passwordPermission = personEntity.PasswordPermission,
                phone = personEntity.Phone ?? string.Empty,
                role = personEntity.Role,
                tag = personEntity.Tag ?? string.Empty
            };
        }

        internal static PersonEntity ToPersonEntity(this Person person)
        {
            if (person == null)
                throw new ArgumentNullException(nameof(person));

            return new PersonEntity()
            {
                CreateTime = person.createTime,
                FaceAndCardPermission = person.faceAndCardPermission,
                FaceAndPasswordPermission = person.faceAndPasswordPermission,
                FacePermission = person.facePermission,
                Id = person.id ?? string.Empty,
                IdcardNum = person.idcardNum ?? string.Empty,
                IdCardPermission = person.idCardPermission,
                IDNumber = person.iDNumber ?? string.Empty,
                Name = person.name ?? string.Empty,
                Phone = person.phone ?? string.Empty,
                Password = person.password ?? string.Empty,
                PasswordPermission = person.passwordPermission,
                Role = person.role,
                Tag = person.tag ?? string.Empty
            };
        }

        internal static Record ToRecord(this RecordEntity recordEntity)
        {
            if (recordEntity == null)
                throw new ArgumentNullException(nameof(recordEntity));

            return new Record
            {
                id = recordEntity.Id ?? string.Empty,
                personId = recordEntity.PersonId ?? string.Empty,
                name = recordEntity.Name ?? string.Empty,
                idcardNum = recordEntity.IdcardNum ?? string.Empty,
                time = recordEntity.Time,
                aliveType = recordEntity.AliveType,
                identifyType = recordEntity.IdentifyType,
                isImgDeleted = recordEntity.IsImgDeleted,
                isPass = recordEntity.IsPass,
                model = recordEntity.Model,
                passTimeType = recordEntity.PassTimeType,
                path = recordEntity.Path ?? string.Empty,
                permissionTimeType = recordEntity.PermissionTimeType,
                recModeType = recordEntity.RecModeType,
                recType = recordEntity.RecType,
                state = recordEntity.State,
                type = recordEntity.Type
            };
        }

        internal static RecordEntity ToRecordEntity(this Record record)
        {
            if (record == null)
                throw new ArgumentNullException(nameof(record));

            return new RecordEntity
            {
                Id = record.id ?? string.Empty,
                PersonId = record.personId ?? string.Empty,
                Name = record.name ?? string.Empty,
                IdcardNum = record.idcardNum ?? string.Empty,
                Time = record.time,
                AliveType = record.aliveType,
                IdentifyType = record.identifyType,
                IsImgDeleted = record.isImgDeleted,
                IsPass = record.isPass,
                Model = record.model,
                PassTimeType = record.passTimeType,
                Path = record.path ?? string.Empty,
                PermissionTimeType = record.permissionTimeType,
                RecModeType = record.recModeType,
                RecType = record.recType,
                State = record.state,
                Type = record.type
            };
        }
    }
}
