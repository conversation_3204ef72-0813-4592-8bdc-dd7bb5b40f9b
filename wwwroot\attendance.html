<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>考勤查询系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 100%;
            margin: 0 auto;
            padding: 10px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 24px;
            font-weight: 600;
        }

        .section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .section-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 15px 20px;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .person-list {
            max-height: 60vh;
            overflow-y: auto;
        }

        .person-item {
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .person-item:last-child {
            border-bottom: none;
        }

        .person-item:hover {
            background: #f8f9fa;
            transform: translateX(5px);
        }

        .person-item:active {
            background: #e9ecef;
        }

        .person-name {
            font-size: 16px;
            font-weight: 500;
            color: #2c3e50;
        }

        .arrow {
            color: #6c757d;
            font-size: 18px;
        }

        .loading {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .calendar-container {
            padding: 20px;
        }

        .calendar-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .month-nav {
            background: #4facfe;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .month-nav:hover {
            background: #3d8bfe;
            transform: scale(1.05);
        }

        .month-nav:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .current-month {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }

        .calendar {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 2px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }

        .calendar-day-header {
            background: #495057;
            color: white;
            padding: 10px 5px;
            text-align: center;
            font-size: 12px;
            font-weight: 600;
        }

        .calendar-day {
            background: white;
            padding: 12px 5px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            min-height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-size: 14px;
        }

        .calendar-day:hover {
            background: #f8f9fa;
        }

        .calendar-day.other-month {
            color: #ccc;
            background: #f8f9fa;
        }

        .calendar-day.has-record {
            background: #d4edda;
            color: #155724;
            font-weight: 600;
        }

        .calendar-day.has-record:hover {
            background: #c3e6cb;
        }

        .calendar-day.abnormal {
            background: #f8d7da;
            color: #721c24;
            font-weight: 600;
        }

        .calendar-day.abnormal:hover {
            background: #f1b0b7;
        }

        .calendar-day.today {
            border: 2px solid #4facfe;
        }

        .record-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #28a745;
        }

        .abnormal-indicator {
            background: #dc3545;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: white;
            margin: 10% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        }

        .modal-header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-title {
            font-size: 18px;
            font-weight: 600;
        }

        .close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: 20px;
            max-height: 60vh;
            overflow-y: auto;
        }

        .record-item {
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
        }

        .record-time {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .record-details {
            font-size: 14px;
            color: #6c757d;
        }

        .error-message {
            text-align: center;
            padding: 40px 20px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 10px;
            margin: 20px;
        }

        .hidden {
            display: none;
        }

        /* 移动端优化 */
        @media (max-width: 768px) {
            .container {
                padding: 5px;
            }

            .header h1 {
                font-size: 20px;
            }

            .section-header {
                padding: 12px 15px;
                font-size: 16px;
            }

            .person-item {
                padding: 12px 15px;
            }

            .calendar-day {
                min-height: 40px;
                font-size: 13px;
            }

            .calendar-day-header {
                padding: 8px 3px;
                font-size: 11px;
            }

            .modal-content {
                margin: 5% auto;
                width: 95%;
            }

            .month-nav {
                padding: 8px 12px;
                font-size: 14px;
            }

            .current-month {
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面标题 -->
        <div class="header">
            <h1>考勤查询系统</h1>
        </div>

        <!-- 人员列表页面 -->
        <div id="personListSection" class="section">
            <div class="section-header">
                <span>选择查询人员</span>
            </div>
            <div id="personList" class="person-list">
                <div class="loading">
                    <div class="spinner"></div>
                    正在加载人员列表...
                </div>
            </div>
        </div>

        <!-- 考勤日历页面 -->
        <div id="calendarSection" class="section hidden">
            <div class="section-header">
                <span id="selectedPersonName">考勤记录</span>
                <button class="back-btn" onclick="showPersonList()">返回</button>
            </div>
            <div class="calendar-container">
                <div class="calendar-header">
                    <button class="month-nav" id="prevMonth" onclick="changeMonth(-1)">‹</button>
                    <div class="current-month" id="currentMonth"></div>
                    <button class="month-nav" id="nextMonth" onclick="changeMonth(1)">›</button>
                </div>
                <div class="calendar" id="calendar"></div>
            </div>
        </div>
    </div>

    <!-- 考勤记录详情模态框 -->
    <div id="recordModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title" id="modalTitle">考勤记录详情</div>
                <button class="close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- 记录详情将在这里显示 -->
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let currentPersonName = '';
        let currentDate = new Date();
        let attendanceData = {};
        let allRecords = [];

        // API基础URL
        const API_BASE = 'http://*************:5002/api';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPersonList();
        });

        // 加载人员列表
        async function loadPersonList() {
            try {
                const response = await fetch(`${API_BASE}/persons`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const persons = await response.json();

                const personListElement = document.getElementById('personList');

                if (persons && persons.length > 0) {
                    personListElement.innerHTML = persons.map(person => `
                        <div class="person-item" onclick="selectPerson('${person.name}')">
                            <div class="person-name">${person.name}</div>
                            <div class="arrow">›</div>
                        </div>
                    `).join('');
                } else {
                    personListElement.innerHTML = '<div class="error-message">暂无人员数据</div>';
                }
            } catch (error) {
                console.error('加载人员列表失败:', error);
                document.getElementById('personList').innerHTML =
                    '<div class="error-message">加载人员列表失败，请检查网络连接</div>';
            }
        }

        // 选择人员
        async function selectPerson(personName) {
            currentPersonName = personName;
            document.getElementById('selectedPersonName').textContent = `${personName} - 考勤记录`;

            // 显示加载状态
            document.getElementById('personListSection').classList.add('hidden');
            document.getElementById('calendarSection').classList.remove('hidden');

            // 重置到当前月份
            currentDate = new Date();

            // 加载考勤数据
            await loadAttendanceData();
            renderCalendar();
        }

        // 加载考勤数据（近3个月）
        async function loadAttendanceData() {
            try {
                // 计算3个月前的日期
                const endDate = new Date();
                const startDate = new Date();
                startDate.setMonth(startDate.getMonth() - 3);

                const startTimeStr = startDate.toISOString().split('T')[0];
                const endTimeStr = endDate.toISOString().split('T')[0];

                const response = await fetch(
                    `${API_BASE}/records/${encodeURIComponent(currentPersonName)}?startTime=${startTimeStr}&endTime=${endTimeStr}`
                );

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                allRecords = await response.json();
                processAttendanceData();

            } catch (error) {
                console.error('加载考勤数据失败:', error);
                allRecords = [];
                attendanceData = {};
            }
        }

        // 处理考勤数据
        function processAttendanceData() {
            attendanceData = {};

            allRecords.forEach(record => {
                const date = record.Time.split(' ')[0]; // 提取日期部分
                const time = record.Time.split(' ')[1]; // 提取时间部分

                if (!attendanceData[date]) {
                    attendanceData[date] = [];
                }

                attendanceData[date].push({
                    time: time,
                    fullTime: record.Time,
                    id: record.Id,
                    personId: record.PersonId,
                    idcardNum: record.IdcardNum
                });
            });

            // 对每天的记录按时间排序
            Object.keys(attendanceData).forEach(date => {
                attendanceData[date].sort((a, b) => a.time.localeCompare(b.time));
            });
        }

        // 判断是否为异常考勤
        function isAbnormalAttendance(date, records) {
            const dateObj = new Date(date);
            const dayOfWeek = dateObj.getDay();

            // 周六(6)和周日(0)不需要考勤
            if (dayOfWeek === 0 || dayOfWeek === 6) {
                return false;
            }

            if (!records || records.length === 0) {
                return false; // 没有记录不算异常，可能是请假
            }

            // 检查异常条件
            const firstTime = records[0].time;
            const lastTime = records[records.length - 1].time;

            // 条件1: 首次打卡晚于9:00
            const isLateArrival = firstTime > '09:00:00';

            // 条件2: 最后打卡早于17:30
            const isEarlyLeave = lastTime < '17:30:00';

            // 条件3: 打卡记录少于2条
            const insufficientRecords = records.length < 2;

            return isLateArrival || isEarlyLeave || insufficientRecords;
        }

        // 渲染日历
        function renderCalendar() {
            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();

            // 更新月份显示
            document.getElementById('currentMonth').textContent =
                `${year}年${month + 1}月`;

            // 获取当月第一天和最后一天
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0);
            const firstDayOfWeek = firstDay.getDay();

            // 获取上个月的最后几天
            const prevMonthLastDay = new Date(year, month, 0).getDate();

            const calendar = document.getElementById('calendar');
            calendar.innerHTML = '';

            // 添加星期标题
            const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
            weekDays.forEach(day => {
                const dayHeader = document.createElement('div');
                dayHeader.className = 'calendar-day-header';
                dayHeader.textContent = day;
                calendar.appendChild(dayHeader);
            });

            // 添加上个月的日期
            for (let i = firstDayOfWeek - 1; i >= 0; i--) {
                const day = prevMonthLastDay - i;
                const dayElement = createDayElement(day, true);
                calendar.appendChild(dayElement);
            }

            // 添加当月的日期
            for (let day = 1; day <= lastDay.getDate(); day++) {
                const dayElement = createDayElement(day, false);
                calendar.appendChild(dayElement);
            }

            // 添加下个月的日期（填满6行）
            const totalCells = calendar.children.length - 7; // 减去星期标题行
            const remainingCells = 42 - totalCells; // 6行 * 7列 - 已有的单元格
            for (let day = 1; day <= remainingCells; day++) {
                const dayElement = createDayElement(day, true);
                calendar.appendChild(dayElement);
            }

            // 更新导航按钮状态
            updateNavigationButtons();
        }

        // 创建日期元素
        function createDayElement(day, isOtherMonth) {
            const dayElement = document.createElement('div');
            dayElement.className = 'calendar-day';
            dayElement.textContent = day;

            if (isOtherMonth) {
                dayElement.classList.add('other-month');
                return dayElement;
            }

            const year = currentDate.getFullYear();
            const month = currentDate.getMonth();
            const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;

            // 检查是否是今天
            const today = new Date();
            if (year === today.getFullYear() &&
                month === today.getMonth() &&
                day === today.getDate()) {
                dayElement.classList.add('today');
            }

            // 检查是否有考勤记录
            const records = attendanceData[dateStr];
            if (records && records.length > 0) {
                dayElement.classList.add('has-record');

                // 检查是否异常
                if (isAbnormalAttendance(dateStr, records)) {
                    dayElement.classList.add('abnormal');
                }

                // 添加点击事件
                dayElement.onclick = () => showDayRecords(dateStr, records);
                dayElement.style.cursor = 'pointer';

                // 添加记录指示器
                const indicator = document.createElement('div');
                indicator.className = 'record-indicator';
                if (isAbnormalAttendance(dateStr, records)) {
                    indicator.classList.add('abnormal-indicator');
                }
                dayElement.appendChild(indicator);
            }

            return dayElement;
        }

        // 显示某天的考勤记录
        function showDayRecords(date, records) {
            const modal = document.getElementById('recordModal');
            const modalTitle = document.getElementById('modalTitle');
            const modalBody = document.getElementById('modalBody');

            modalTitle.textContent = `${date} 考勤记录`;

            const isAbnormal = isAbnormalAttendance(date, records);

            let html = '';
            if (isAbnormal) {
                html += '<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 8px; margin-bottom: 15px; font-weight: 600;">⚠️ 异常考勤</div>';
            }

            html += records.map(record => `
                <div class="record-item">
                    <div class="record-time">${record.time}</div>
                    <div class="record-details">
                        完整时间: ${record.fullTime}<br>
                        人员ID: ${record.personId}<br>
                        身份证号: ${record.idcardNum || '未知'}
                    </div>
                </div>
            `).join('');

            // 添加异常分析
            if (isAbnormal) {
                html += '<div style="margin-top: 15px; padding: 10px; background: #fff3cd; border-radius: 8px;">';
                html += '<strong>异常原因分析:</strong><br>';

                const firstTime = records[0].time;
                const lastTime = records[records.length - 1].time;

                if (firstTime > '09:00:00') {
                    html += `• 首次打卡时间 ${firstTime} 晚于 09:00<br>`;
                }
                if (lastTime < '17:30:00') {
                    html += `• 最后打卡时间 ${lastTime} 早于 17:30<br>`;
                }
                if (records.length < 2) {
                    html += `• 当天打卡记录只有 ${records.length} 条，少于正常的2条<br>`;
                }

                html += '</div>';
            }

            modalBody.innerHTML = html;
            modal.style.display = 'block';
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('recordModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('recordModal');
            if (event.target === modal) {
                closeModal();
            }
        }

        // 切换月份
        function changeMonth(direction) {
            currentDate.setMonth(currentDate.getMonth() + direction);
            renderCalendar();
        }

        // 更新导航按钮状态
        function updateNavigationButtons() {
            const now = new Date();
            const threeMonthsAgo = new Date();
            threeMonthsAgo.setMonth(threeMonthsAgo.getMonth() - 3);

            // 禁用超出范围的按钮
            const prevBtn = document.getElementById('prevMonth');
            const nextBtn = document.getElementById('nextMonth');

            // 不能查看3个月前的数据
            if (currentDate <= threeMonthsAgo) {
                prevBtn.disabled = true;
            } else {
                prevBtn.disabled = false;
            }

            // 不能查看未来的数据
            if (currentDate >= now) {
                nextBtn.disabled = true;
            } else {
                nextBtn.disabled = false;
            }
        }

        // 返回人员列表
        function showPersonList() {
            document.getElementById('calendarSection').classList.add('hidden');
            document.getElementById('personListSection').classList.remove('hidden');
            currentPersonName = '';
            attendanceData = {};
            allRecords = [];
        }
    </script>
</body>
</html>
