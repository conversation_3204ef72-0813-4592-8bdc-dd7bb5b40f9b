using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using attendance.DataAccess;

namespace attendance.Services
{
    public class PersonSyncService : BackgroundService
    {
        private readonly ILogger<PersonSyncService> _logger;
        private readonly FaceDeviceService _deviceService;
        private readonly PersonRepository _personRepository;
        private readonly ConcurrentDictionary<string, List<Person>> _personCache;

        // 可配置的同步间隔（分钟）
        private int _syncIntervalMinutes = 5;

        public PersonSyncService(
            ILogger<PersonSyncService> logger,
            FaceDeviceService deviceService,
            PersonRepository personRepository)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _deviceService = deviceService ?? throw new ArgumentNullException(nameof(deviceService));
            _personRepository = personRepository ?? throw new ArgumentNullException(nameof(personRepository));
            _personCache = new ConcurrentDictionary<string, List<Person>>();
        }

        // 提供修改同步间隔的方法
        public void SetSyncInterval(int minutes)
        {
            if (minutes < 1)
            {
                throw new ArgumentException("Interval must be at least 1 minute", nameof(minutes));
            }
            _syncIntervalMinutes = minutes;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await SyncPersonsAsync(stoppingToken);
                    await Task.Delay(TimeSpan.FromMinutes(_syncIntervalMinutes), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    _logger.LogInformation("Person synchronization service is stopping");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Fatal error in person synchronization service");
                    throw;
                }
            }

        }

        private async Task SyncPersonsAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 从设备获取最新人员列表
                var currentPersons = _deviceService.GetPersons();
                if (currentPersons == null || !currentPersons.Any())
                {
                    _logger.LogInformation("No persons found in device");
                    return;
                }


                // 获取缓存的上一次人员列表
                var lastPersons = _personCache.GetOrAdd("persons", new List<Person>());
                if (!lastPersons.Any())
                {
                    // 如果缓存为空，从数据库加载人员信息
                    lastPersons = _personRepository.GetAll().Select(p => p.ToPerson()).ToList();
                    _personCache["persons"] = lastPersons;
                }

                // 找出新增的人员
                var newPersons = currentPersons
                    .Where(cp => !lastPersons.Any(lp => lp.id == cp.id))
                    .ToList();

                // 找出更新的人员
                var updatedPersons = currentPersons
                    .Where(cp => lastPersons.Any(lp =>
                        lp.id == cp.id && IsPersonChanged(cp, lp)))
                    .ToList();

                // 处理新增人员
                if (newPersons.Any())
                {
                    _logger.LogInformation($"Found {newPersons.Count} new persons");
                    foreach (var person in newPersons)
                    {
                        try
                        {
                            var entity = person.ToPersonEntity();
                            await Task.Run(() => _personRepository.InsertOrUpdate(entity), cancellationToken);
                            _logger.LogInformation($"New person added: {person.id} ({person.name})");
                        }
                        catch (Exception ex) when (ex is not OperationCanceledException)
                        {
                            _logger.LogError(ex, $"Error adding new person: {person.id}");
                        }
                    }
                }

                // 处理更新的人员
                if (updatedPersons.Any())
                {
                    _logger.LogInformation($"Found {updatedPersons.Count} updated persons");
                    foreach (var person in updatedPersons)
                    {
                        try
                        {
                            var entity = person.ToPersonEntity();
                            await Task.Run(() => _personRepository.InsertOrUpdate(entity), cancellationToken);
                            _logger.LogInformation($"Person updated: {person.id} ({person.name})");
                        }
                        catch (Exception ex) when (ex is not OperationCanceledException)
                        {
                            _logger.LogError(ex, $"Error updating person: {person.id}");
                        }
                    }
                }

                // 更新缓存
                _personCache["persons"] = currentPersons;


                _logger.LogInformation($"Person sync completed. Total: {currentPersons.Count}, New: {newPersons.Count}, Updated: {updatedPersons.Count}");
            }
            catch (Exception ex) when (ex is not OperationCanceledException)
            {
                _logger.LogError(ex, "Error in SyncPersonsAsync");
                throw;
            }
        }

        private static bool IsPersonChanged(Person current, Person last)
        {
            if (current == null) throw new ArgumentNullException(nameof(current));
            if (last == null) throw new ArgumentNullException(nameof(last));

            return current.name != last.name ||
                   current.idcardNum != last.idcardNum ||
                   current.role != last.role;
        }


    }
}
