using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace attendance
{
    public class FaceDeviceService
    {

        private const string PERSON_FIND = "/person/find"; //人员查询

        private const string PERSON_CREATE = "/person/create"; //人员添加

        private const string PERSON_DELETE = "/person/delete"; //人员删除

        private const string PERSON_UPDATE = "/person/update"; //人员更新

        private const string FACE_CREATE = "/face/create"; //人脸注册

        private const string IDENTIFY_CALLBACK = "/setIdentifyCallBack";//识别回调

        private const string RECORD_FIND = "/findRecords";//刷脸记录查询

        private const string NEW_RECORD_FIND = "/newFindRecords"; //新的识别记录查询接口

        private const string NEW_RECORD_DELETE = "/newDeleteRecords"; //新识别记录删除

        private const string FACE_FIND = "/face/find"; //人脸照片查询

        private const string FACE_DELETE = "/face/delete"; //照片删除

        private const string FACE_DELETE_BY_PERSON = "/face/deletePerson";//清空指定人员注册的所有照片

        private const string PASSWORD_SET = "/setPassword";//设备密码设置

        private const string DEVICE_INFO = "/device/information"; // 设备信息查询
        private const string HEARTBEAT_SET = "/setDeviceHeartBeat"; //心跳回调设置

        private const string TASK_INTERFACE_ADDRESS_SET = "/setTaskInterfaceAddress"; //任务获取地址设置

        private const string TASK_PROCESSING_RESULT_ADDRESS_SET = "/setTaskProcessingResultsAddress"; //任务处理结果回调地址设置

        private const string FACE_REGISTER = "/face/create"; //照片注册接口

        private const string FACE_REGISTER_BY_URL = "/face/createByUrl"; //通过url注册人脸

        private const string FACE_UPDATE = "/face/update";  // 照片更新

        private const string FACE_COMPARISON = "/photoComparison"; //人像相似度比对

        private const string DEVICE_CONFIG = "/setConfig";  // 设备配置

        private const string PASS_SET = "/setPassWord"; //设置密码

        private const string IDENTIFY_CALLBACK_SET = "/setIdentifyCallBack"; //设置识别回调

        private const string IMG_REG_CALLBACK_SET = "/setImgRegCallBack"; // 照片注册回调

        private const string CALLBACKS_GET = "/device/callback"; //回调地址查询

        private const string OPEN_DOOR_CONTROL = "/device/openDoorControl"; // 远程控制输出

        private AppSettings appsettings;

        public FaceDeviceService(IOptions<AppSettings> options)
        {
            this.appsettings = options.Value;
        }

        #region 人员类

        /// <summary>
        /// 人员注册
        /// </summary>
        /// <param name="person"></param>
        /// <returns></returns>
        public BaseResponseResult<Person> CreatePerson(Person person)
        {
            string pass = appsettings.Password;
            string jsonData = JsonConvert.SerializeObject(person);

            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", pass);
            para.Add("person", jsonData);
            return RestService.PostAsyncByForm<BaseResponseResult<Person>>(appsettings.BaseUrl + PERSON_CREATE, para).Result;
        }

        /// <summary>
        /// 删除人员信息,多个人员id用逗号分隔
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        public BaseResponseResult<String> DeletePerson(string personId)
        {
            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);

            var builder = new UriBuilder(appsettings.BaseUrl + PERSON_DELETE);
            return RestService.PostAsyncByForm<BaseResponseResult<String>>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 人员更新
        /// </summary>
        /// <param name="person"></param>
        /// <returns></returns>
        public BaseResponseResult<Person> UpdatePerson(Person person)
        {
            string pass = appsettings.Password;
            string jsonData = JsonConvert.SerializeObject(person);

            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", pass);
            para.Add("person", jsonData);
            return RestService.PostAsyncByForm<BaseResponseResult<Person>>(appsettings.BaseUrl + PERSON_UPDATE, para).Result;

        }

        // 获取所有人员列表
        public List<Person> GetPersons()
        {
            string personUrl = $"{appsettings.BaseUrl}{PERSON_FIND}?pass={appsettings.Password}&id=-1";
            return RestService.GetAsync<PersonResult>(personUrl).Result.data;
        }

        #endregion

        #region 照片类接口

        /// <summary>
        /// 照片注册(Base64),不加头部，如：data:image/jpg;base64,图片格式支持 png、jpg、jpeg
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="faceId"></param>
        /// <param name="imgBase64"></param>
        /// <param name="isEasyway"></param>
        /// <returns></returns>
        public BaseResponseResult<string> FaceRegister(string personId, string faceId, string imgBase64, bool isEasyway = false)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);
            para.Add("faceId", faceId);
            para.Add("imgBase64", imgBase64);
            para.Add("isEasyWay", isEasyway.ToString());

            var builder = new UriBuilder(appsettings.BaseUrl + FACE_REGISTER);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 通过URL注册人脸
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="faceId"></param>
        /// <param name="imgUrl"></param>
        /// <param name="isEasyway"></param>
        /// <returns></returns>
        public BaseResponseResult<string> FaceRegisterByUrl(string personId, string faceId, string imgUrl, bool isEasyway = false)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);
            para.Add("faceId", faceId);
            para.Add("imgUrl", imgUrl);
            para.Add("isEasyWay", isEasyway.ToString());
            var builder = new UriBuilder(appsettings.BaseUrl + FACE_REGISTER_BY_URL);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.Uri.ToString(), para).Result;

        }

        /// <summary>
        /// 根据指定的faceId 删除人脸
        /// </summary>
        /// <param name="faceId"></param>
        /// <returns></returns>
        public BaseResponseResult<string> FaceDelete(string faceId)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("faceId", faceId);

            var builder = new UriBuilder(appsettings.BaseUrl + FACE_DELETE);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.Uri.ToString(), para).Result;

        }

        /// <summary>
        /// 人脸更新
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="faceId"></param>
        /// <param name="imgBase64"></param>
        /// <param name="isEasyway"></param>
        /// <returns></returns>
        public BaseResponseResult<string> FaceUpdate(string personId, string faceId, string imgBase64, bool isEasyway = false)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);
            para.Add("faceId", faceId);
            para.Add("imgBase64", imgBase64);
            para.Add("isEasyWay", isEasyway.ToString());
            var builder = new UriBuilder(appsettings.BaseUrl + FACE_UPDATE);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 查找人脸
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        public BaseResponseResult<List<FaceFindData>> FaceFind(string personId)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);
            var builder = new UriBuilder(appsettings.BaseUrl + FACE_FIND);
            return RestService.PostAsyncByForm<BaseResponseResult<List<FaceFindData>>>(builder.Uri.ToString(), para).Result;

        }

        /// <summary>
        /// 删除指定人员的所有人脸
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        public BaseResponseResult<string> DeleteFaceByPerson(string personId)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);
            var builder = new UriBuilder(appsettings.BaseUrl + FACE_DELETE_BY_PERSON);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.ToString(), para).Result;
        }

        /// <summary>
        /// 人员相似度比对
        /// </summary>
        /// <param name="img1">照片的base64编码,不加头部</param>
        /// <param name="img2">照片的base64编码,不加头部</param>
        /// <returns>比对成功或失败</returns>
        public BaseResponseResult<string> FaceComparison(string img1, string img2)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("img1", img1);
            para.Add("img2", img2);
            var builder = new UriBuilder(appsettings.BaseUrl + FACE_COMPARISON);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.ToString(), para).Result;
        }

        #endregion

        #region 识别记录
        /// <summary>
        /// 获取识别记录
        /// </summary>
        /// <param name="personId">查询指定id的人员识别记录,传-1查询所有人,传STRANGERBABY查陌生人和识别失败的记录</param>
        /// <param name="startTime">查询开始时间,不按时间则传0</param>
        /// <param name="endTime">查询结束时间,不按时间则传0</param>
        /// <param name="length">每页最大条数,(0,1000)的范围,不传默认值为1000</param>
        /// <param name="model"> 
        /// -1：所有类型的识别记录
        /// 0：刷脸识别
        /// 1：卡&人像双重认证（人卡合一）
        /// 2：人证比对
        /// 3：刷卡识别
        /// 4 开门按钮开门
        /// 5 远程开门
        /// 6 密码开门
        /// 7 人像&密码双重验证
        /// 8 口罩检测
        /// 9 指纹比对
        /// model 不传则默认为-1
        ///  </param>
        /// <param name="order">1按时间升序排列,不传或传1以外的按降序排列</param>
        /// <param name="index">页码,从0开始</param>
        /// <returns></returns>
        public NewRecordFindResult GetRecordResult(string personId, string startTime, string endTime, int? length, int? model, string? order, int? index)
        {
            string passParam = $"pass={appsettings.Password}";
            string startTimePara = !string.IsNullOrWhiteSpace(startTime) ? $"&startTime={startTime}" : "&startTime=0";
            string endTimePara = !string.IsNullOrWhiteSpace(endTime) ? $"&endTime={endTime}" : "&endTime=0";
            string personIdPara = !string.IsNullOrWhiteSpace(personId) ? $"&personId={personId}" : "&personId=-1";
            string lengthPara = length.HasValue ? $"&length={length}" : "&length=1000";
            string modelPara = model.HasValue ? $"&model={model}" : "&model=-1";
            string orderPara = !string.IsNullOrWhiteSpace(order) ? $"&order={order}" : "";
            string indexPara = index.HasValue ? $"&index={index}" : "&index=0";

            var builder = new UriBuilder(appsettings.BaseUrl + NEW_RECORD_FIND);
            builder.Query = passParam + personIdPara + startTimePara + endTimePara + lengthPara + modelPara + orderPara + indexPara;

            string recordFindURL = builder.Uri.ToString();

            return RestService.GetAsync<NewRecordFindResult>(recordFindURL).Result;
        }

        /// <summary>
        /// 查询指定人员的识别记录
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        public NewRecordFindResult GetRecordResultByPerson(string personId)
        {
            return GetRecordResult(personId, "", "", null, null, null, null);
        }


        /// <summary>
        /// 根据开始时间查询指定人员的识别记录
        /// </summary>
        /// <param name="personId"></param>
        /// <returns></returns>
        public NewRecordFindResult GetRecordResultByPersonAndStartTime(string personId, string startTime)
        {
            return GetRecordResult(personId, startTime, "", null, null, null, null);
        }

        /// <summary>
        /// 删除指定人员的识别记录
        /// </summary>
        /// <param name="personId"></param>
        /// <param name="startTime"></param>
        /// <param name="endTime"></param>
        /// <param name="model"></param>
        /// <returns></returns>

        public NewRecordDeleteResult DeleteRecord(string personId, string startTime, string endTime, int? model)
        {
            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("personId", personId);
            para.Add("startTime", startTime);
            para.Add("endTime", endTime);
            model = model.HasValue ? model : -1;
            para.Add("model", model.ToString());
            var builder = new UriBuilder(appsettings.BaseUrl + NEW_RECORD_DELETE);
            return RestService.PostAsyncByForm<NewRecordDeleteResult>(builder.Uri.ToString(), para).Result;
        }

        #endregion

        #region 设备类接口

        public BaseResponseResult<FaceDeviceConfig> SetDeviceConfig(string jsonDeviceConfig)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("Config", jsonDeviceConfig);
            var builder = new UriBuilder(appsettings.BaseUrl, DEVICE_CONFIG);
            return RestService.PostAsyncByForm<BaseResponseResult<FaceDeviceConfig>>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 设置心跳回调
        /// </summary>
        /// <param name="url"></param>
        /// <param name="interval"></param>
        /// <returns></returns>
        public SetHeartbeatResult SetHeartbeat(string url, int interval)
        {
            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("url", url);
            para.Add("interval", interval.ToString());
            var builder = new UriBuilder(appsettings.BaseUrl + HEARTBEAT_SET);
            return RestService.PostAsyncByForm<SetHeartbeatResult>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 设置任务回调URL地址
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public TaskInterfaceAddressSetResult SetTaskInterfaceAddress(string url)
        {
            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("url", url);
            var builder = new UriBuilder(appsettings.BaseUrl + TASK_INTERFACE_ADDRESS_SET);
            return RestService.PostAsyncByForm<TaskInterfaceAddressSetResult>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 设置任务处理结果回调URL地址
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>

        public TaskProcessingResultAddressSetResult SetTaskProcessingResultAddress(string url)
        {
            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("url", url);
            var builder = new UriBuilder(appsettings.BaseUrl + TASK_PROCESSING_RESULT_ADDRESS_SET);
            return RestService.PostAsyncByForm<TaskProcessingResultAddressSetResult>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 设置识别回调地址
        /// </summary>
        /// <param name="callbackUrl"></param>
        /// <param name="base64Enable"></param>
        /// <returns></returns>
        public BaseResponseResult<string> SetIdentifyCallback(string callbackUrl, int base64Enable)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("callbackUrl", callbackUrl);
            para.Add("base64Enable", base64Enable.ToString());

            var builder = new UriBuilder(appsettings.BaseUrl + IDENTIFY_CALLBACK_SET);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.Uri.ToString(), para).Result;

        }

        /// <summary>
        /// 设置照片注册回调
        /// </summary>
        /// <param name="url"></param>
        /// <param name="base64Enable"></param>
        /// <returns></returns>

        public BaseResponseResult<string> SetImgCallback(string url, int base64Enable)
        {
            var para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("url", url);
            para.Add("base64Enable", base64Enable.ToString());
            var builder = new UriBuilder(appsettings.BaseUrl, IMG_REG_CALLBACK_SET);
            return RestService.PostAsyncByForm<BaseResponseResult<string>>(builder.Uri.ToString(), para).Result;
        }

        /// <summary>
        /// 获取已经注册的回调地址
        /// </summary>
        /// <returns></returns>

        public BaseResponseResult<Dictionary<string, string>> GetCallbacks()
        {
            var builder = new UriBuilder(appsettings.BaseUrl + CALLBACKS_GET);
            builder.Query = $"pass={appsettings.Password}";

            return RestService.GetAsync<BaseResponseResult<Dictionary<string, string>>>(builder.Uri.ToString()).Result;

        }

        #region 设备控制

        /// <summary>
        /// 远程控制输出
        /// </summary>
        /// <param name="type">设备交互类型: 1=开门 2=关门 3=有限 4=表示自定义文字弹框，自定义语音播报；默认为1</param>
        /// <param name="content">输出内容。当type=4时，content={"ttsModContent":"自定义语音","displayModContent":"自定义文字"}</param>
        /// <returns></returns>
        public BaseResponseResult<string> OpenDoorControl(int type = 1, string content = null)
        {
            Dictionary<string, string> para = new Dictionary<string, string>();
            para.Add("pass", appsettings.Password);
            para.Add("type", type.ToString());

            if (!string.IsNullOrEmpty(content))
            {
                para.Add("content", content);
            }

            return RestService.PostAsyncByForm<BaseResponseResult<string>>(appsettings.BaseUrl + OPEN_DOOR_CONTROL, para).Result;
        }

        #endregion

        #endregion

    }

}
